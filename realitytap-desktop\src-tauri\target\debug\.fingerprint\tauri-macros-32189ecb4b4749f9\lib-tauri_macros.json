{"rustc": 10895048813736897673, "features": "[\"compression\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 2225463790103693989, "path": 15262338241553268757, "deps": [[3060637413840920116, "proc_macro2", false, 17515079266447713286], [7341521034400937459, "tauri_codegen", false, 14192029025554880882], [11050281405049894993, "tauri_utils", false, 4484266758743268199], [13077543566650298139, "heck", false, 7607099312003041627], [17990358020177143287, "quote", false, 13800562964788263047], [18149961000318489080, "syn", false, 16190129315601163247]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-macros-32189ecb4b4749f9\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}