{"rustc": 10895048813736897673, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[10755362358622467486, "build_script_build", false, 16644609602963897887], [11721252211900136025, "build_script_build", false, 13898128805552738424]], "local": [{"RerunIfChanged": {"output": "debug\\build\\tauri-plugin-updater-a113e6c5ea047a10\\output", "paths": ["permissions"]}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}