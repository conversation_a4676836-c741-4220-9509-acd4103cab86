["\\\\?\\E:\\03.Codes\\10.AWA\\10.realitytap\\realitytap-ecosystem\\realitytap-desktop\\src-tauri\\target\\debug\\build\\tauri-6a51481995cc3322\\out\\permissions\\path\\autogenerated\\commands\\basename.toml", "\\\\?\\E:\\03.Codes\\10.AWA\\10.realitytap\\realitytap-ecosystem\\realitytap-desktop\\src-tauri\\target\\debug\\build\\tauri-6a51481995cc3322\\out\\permissions\\path\\autogenerated\\commands\\dirname.toml", "\\\\?\\E:\\03.Codes\\10.AWA\\10.realitytap\\realitytap-ecosystem\\realitytap-desktop\\src-tauri\\target\\debug\\build\\tauri-6a51481995cc3322\\out\\permissions\\path\\autogenerated\\commands\\extname.toml", "\\\\?\\E:\\03.Codes\\10.AWA\\10.realitytap\\realitytap-ecosystem\\realitytap-desktop\\src-tauri\\target\\debug\\build\\tauri-6a51481995cc3322\\out\\permissions\\path\\autogenerated\\commands\\is_absolute.toml", "\\\\?\\E:\\03.Codes\\10.AWA\\10.realitytap\\realitytap-ecosystem\\realitytap-desktop\\src-tauri\\target\\debug\\build\\tauri-6a51481995cc3322\\out\\permissions\\path\\autogenerated\\commands\\join.toml", "\\\\?\\E:\\03.Codes\\10.AWA\\10.realitytap\\realitytap-ecosystem\\realitytap-desktop\\src-tauri\\target\\debug\\build\\tauri-6a51481995cc3322\\out\\permissions\\path\\autogenerated\\commands\\normalize.toml", "\\\\?\\E:\\03.Codes\\10.AWA\\10.realitytap\\realitytap-ecosystem\\realitytap-desktop\\src-tauri\\target\\debug\\build\\tauri-6a51481995cc3322\\out\\permissions\\path\\autogenerated\\commands\\resolve.toml", "\\\\?\\E:\\03.Codes\\10.AWA\\10.realitytap\\realitytap-ecosystem\\realitytap-desktop\\src-tauri\\target\\debug\\build\\tauri-6a51481995cc3322\\out\\permissions\\path\\autogenerated\\commands\\resolve_directory.toml", "\\\\?\\E:\\03.Codes\\10.AWA\\10.realitytap\\realitytap-ecosystem\\realitytap-desktop\\src-tauri\\target\\debug\\build\\tauri-6a51481995cc3322\\out\\permissions\\path\\autogenerated\\default.toml"]