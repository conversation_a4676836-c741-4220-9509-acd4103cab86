{"rustc": 10895048813736897673, "features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"charset\", \"default\", \"default-tls\", \"h2\", \"http2\", \"json\", \"macos-system-configuration\", \"rustls-tls\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"stream\"]", "declared_features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"blocking\", \"brotli\", \"charset\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"gzip\", \"h2\", \"hickory-dns\", \"http2\", \"http3\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-manual-roots-no-provider\", \"rustls-tls-native-roots\", \"rustls-tls-native-roots-no-provider\", \"rustls-tls-no-provider\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"socks\", \"stream\", \"trust-dns\", \"zstd\"]", "target": 8885864859914201979, "profile": 15302557990823967831, "path": 12657710285491601278, "deps": [[40386456601120721, "percent_encoding", false, 11430224217469457874], [95042085696191081, "ipnet", false, 13007959173986590061], [418947936956741439, "h2", false, 5245101362361320814], [784494742817713399, "tower_service", false, 14331164374895411977], [1288403060204016458, "tokio_util", false, 17148746417079904665], [1906322745568073236, "pin_project_lite", false, 8867982021046338078], [2517136641825875337, "sync_wrapper", false, 8468070649238578696], [2883436298747778685, "rustls_pki_types", false, 8304085847310651148], [3150220818285335163, "url", false, 5408171043244425519], [3722963349756955755, "once_cell", false, 11521336827388356010], [5138218615291878843, "tokio", false, 15250874349822087297], [5695049318159433696, "tower", false, 7854687765387026132], [5986029879202738730, "log", false, 15354736063082002461], [7161480121686072451, "rustls", false, 76033564079317628], [7620660491849607393, "futures_core", false, 3426348630773557045], [8156804143951879168, "webpki_roots", false, 11154638724317979613], [9010263965687315507, "http", false, 16950085000799907330], [9689903380558560274, "serde", false, 3282804803960374299], [10229185211513642314, "mime", false, 17650519331291606697], [10595802073777078462, "hyper_util", false, 14414028822076177131], [10629569228670356391, "futures_util", false, 757868430050288205], [11895591994124935963, "tokio_rustls", false, 2457027739171416468], [11957360342995674422, "hyper", false, 18383867383573369530], [12186126227181294540, "tokio_native_tls", false, 6277211204017331171], [13077212702700853852, "base64", false, 10972977749164998151], [13330729881774643389, "hyper_rustls", false, 17132097471794667520], [14084095096285906100, "http_body", false, 14754084443964931760], [14564311161534545801, "encoding_rs", false, 1677286647306383756], [15032952994102373905, "rustls_pemfile", false, 10512825051105003317], [15367738274754116744, "serde_json", false, 15916909028635074265], [15697835491348449269, "windows_registry", false, 217018039624474009], [16066129441945555748, "bytes", false, 11184222215494284620], [16542808166767769916, "serde_urlencoded", false, 8624081575018438413], [16785601910559813697, "native_tls_crate", false, 9190537096465656121], [16900715236047033623, "http_body_util", false, 17266077411315750162], [18273243456331255970, "hyper_tls", false, 17359340754135412791]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\reqwest-bade043a0f92117e\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}