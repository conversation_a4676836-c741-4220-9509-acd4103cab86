{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[]", "target": 11842472800422696103, "profile": 15657897354478470176, "path": 16644724465513340381, "deps": [[806484002035136204, "gethostname", false, 3495418870527609931], [4919829919303820331, "serialize_to_javascript", false, 17428035850388715376], [5024769281214949041, "os_info", false, 10268233838288788052], [5986029879202738730, "log", false, 15354736063082002461], [9689903380558560274, "serde", false, 3282804803960374299], [10755362358622467486, "tauri", false, 17769680339998947657], [10806645703491011684, "thiserror", false, 9617457741875845117], [12676100885892732016, "build_script_build", false, 970302984841463815], [14618885535728128396, "sys_locale", false, 13030997229419666156], [15367738274754116744, "serde_json", false, 15916909028635074265]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-os-917347dcf0a69855\\dep-lib-tauri_plugin_os", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}