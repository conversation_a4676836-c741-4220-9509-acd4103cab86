["\\\\?\\E:\\03.Codes\\10.AWA\\10.realitytap\\realitytap-ecosystem\\realitytap-desktop\\src-tauri\\target\\debug\\build\\tauri-6a51481995cc3322\\out\\permissions\\menu\\autogenerated\\commands\\append.toml", "\\\\?\\E:\\03.Codes\\10.AWA\\10.realitytap\\realitytap-ecosystem\\realitytap-desktop\\src-tauri\\target\\debug\\build\\tauri-6a51481995cc3322\\out\\permissions\\menu\\autogenerated\\commands\\create_default.toml", "\\\\?\\E:\\03.Codes\\10.AWA\\10.realitytap\\realitytap-ecosystem\\realitytap-desktop\\src-tauri\\target\\debug\\build\\tauri-6a51481995cc3322\\out\\permissions\\menu\\autogenerated\\commands\\get.toml", "\\\\?\\E:\\03.Codes\\10.AWA\\10.realitytap\\realitytap-ecosystem\\realitytap-desktop\\src-tauri\\target\\debug\\build\\tauri-6a51481995cc3322\\out\\permissions\\menu\\autogenerated\\commands\\insert.toml", "\\\\?\\E:\\03.Codes\\10.AWA\\10.realitytap\\realitytap-ecosystem\\realitytap-desktop\\src-tauri\\target\\debug\\build\\tauri-6a51481995cc3322\\out\\permissions\\menu\\autogenerated\\commands\\is_checked.toml", "\\\\?\\E:\\03.Codes\\10.AWA\\10.realitytap\\realitytap-ecosystem\\realitytap-desktop\\src-tauri\\target\\debug\\build\\tauri-6a51481995cc3322\\out\\permissions\\menu\\autogenerated\\commands\\is_enabled.toml", "\\\\?\\E:\\03.Codes\\10.AWA\\10.realitytap\\realitytap-ecosystem\\realitytap-desktop\\src-tauri\\target\\debug\\build\\tauri-6a51481995cc3322\\out\\permissions\\menu\\autogenerated\\commands\\items.toml", "\\\\?\\E:\\03.Codes\\10.AWA\\10.realitytap\\realitytap-ecosystem\\realitytap-desktop\\src-tauri\\target\\debug\\build\\tauri-6a51481995cc3322\\out\\permissions\\menu\\autogenerated\\commands\\new.toml", "\\\\?\\E:\\03.Codes\\10.AWA\\10.realitytap\\realitytap-ecosystem\\realitytap-desktop\\src-tauri\\target\\debug\\build\\tauri-6a51481995cc3322\\out\\permissions\\menu\\autogenerated\\commands\\popup.toml", "\\\\?\\E:\\03.Codes\\10.AWA\\10.realitytap\\realitytap-ecosystem\\realitytap-desktop\\src-tauri\\target\\debug\\build\\tauri-6a51481995cc3322\\out\\permissions\\menu\\autogenerated\\commands\\prepend.toml", "\\\\?\\E:\\03.Codes\\10.AWA\\10.realitytap\\realitytap-ecosystem\\realitytap-desktop\\src-tauri\\target\\debug\\build\\tauri-6a51481995cc3322\\out\\permissions\\menu\\autogenerated\\commands\\remove.toml", "\\\\?\\E:\\03.Codes\\10.AWA\\10.realitytap\\realitytap-ecosystem\\realitytap-desktop\\src-tauri\\target\\debug\\build\\tauri-6a51481995cc3322\\out\\permissions\\menu\\autogenerated\\commands\\remove_at.toml", "\\\\?\\E:\\03.Codes\\10.AWA\\10.realitytap\\realitytap-ecosystem\\realitytap-desktop\\src-tauri\\target\\debug\\build\\tauri-6a51481995cc3322\\out\\permissions\\menu\\autogenerated\\commands\\set_accelerator.toml", "\\\\?\\E:\\03.Codes\\10.AWA\\10.realitytap\\realitytap-ecosystem\\realitytap-desktop\\src-tauri\\target\\debug\\build\\tauri-6a51481995cc3322\\out\\permissions\\menu\\autogenerated\\commands\\set_as_app_menu.toml", "\\\\?\\E:\\03.Codes\\10.AWA\\10.realitytap\\realitytap-ecosystem\\realitytap-desktop\\src-tauri\\target\\debug\\build\\tauri-6a51481995cc3322\\out\\permissions\\menu\\autogenerated\\commands\\set_as_help_menu_for_nsapp.toml", "\\\\?\\E:\\03.Codes\\10.AWA\\10.realitytap\\realitytap-ecosystem\\realitytap-desktop\\src-tauri\\target\\debug\\build\\tauri-6a51481995cc3322\\out\\permissions\\menu\\autogenerated\\commands\\set_as_window_menu.toml", "\\\\?\\E:\\03.Codes\\10.AWA\\10.realitytap\\realitytap-ecosystem\\realitytap-desktop\\src-tauri\\target\\debug\\build\\tauri-6a51481995cc3322\\out\\permissions\\menu\\autogenerated\\commands\\set_as_windows_menu_for_nsapp.toml", "\\\\?\\E:\\03.Codes\\10.AWA\\10.realitytap\\realitytap-ecosystem\\realitytap-desktop\\src-tauri\\target\\debug\\build\\tauri-6a51481995cc3322\\out\\permissions\\menu\\autogenerated\\commands\\set_checked.toml", "\\\\?\\E:\\03.Codes\\10.AWA\\10.realitytap\\realitytap-ecosystem\\realitytap-desktop\\src-tauri\\target\\debug\\build\\tauri-6a51481995cc3322\\out\\permissions\\menu\\autogenerated\\commands\\set_enabled.toml", "\\\\?\\E:\\03.Codes\\10.AWA\\10.realitytap\\realitytap-ecosystem\\realitytap-desktop\\src-tauri\\target\\debug\\build\\tauri-6a51481995cc3322\\out\\permissions\\menu\\autogenerated\\commands\\set_icon.toml", "\\\\?\\E:\\03.Codes\\10.AWA\\10.realitytap\\realitytap-ecosystem\\realitytap-desktop\\src-tauri\\target\\debug\\build\\tauri-6a51481995cc3322\\out\\permissions\\menu\\autogenerated\\commands\\set_text.toml", "\\\\?\\E:\\03.Codes\\10.AWA\\10.realitytap\\realitytap-ecosystem\\realitytap-desktop\\src-tauri\\target\\debug\\build\\tauri-6a51481995cc3322\\out\\permissions\\menu\\autogenerated\\commands\\text.toml", "\\\\?\\E:\\03.Codes\\10.AWA\\10.realitytap\\realitytap-ecosystem\\realitytap-desktop\\src-tauri\\target\\debug\\build\\tauri-6a51481995cc3322\\out\\permissions\\menu\\autogenerated\\default.toml"]